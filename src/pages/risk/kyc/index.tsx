import { CrudTemplate } from "@/components/crud-template";
import type { BaseEntity, CrudColumn, CrudConfig, CrudFormField, CrudHandlers } from "@/components/crud-template";
import { Badge } from "@/ui/badge";
import { z } from "zod";

interface Product extends BaseEntity {
	id: string;
	name: string;
	price: number;
	category: string;
	status: "active" | "inactive";
	createdAt: string;
	updatedAt: string;
}

const columns: CrudColumn<Product>[] = [
	{
		key: "name",
		title: "Product Name",
		sortable: true,
		filterable: true,
		searchable: true,
		filterType: "text",
	},
	{
		key: "price",
		title: "Price",
		sortable: true,
		filterable: true,
		filterType: "number",
		render: (value) => `$${value.toFixed(2)}`,
	},
	{
		key: "status",
		title: "Status",
		filterable: true,
		filterType: "select",
		filterOptions: [
			{ label: "Active", value: "active" },
			{ label: "Inactive", value: "inactive" },
		],
		render: (value) => <Badge variant={value === "active" ? "default" : "secondary"}>{value}</Badge>,
	},
];

const formFields: CrudFormField<Product>[] = [
	{
		key: "name",
		label: "Product Name",
		type: "text",
		required: true,
		placeholder: "Enter product name",
	},
	{
		key: "price",
		label: "Price",
		type: "number",
		required: true,
		placeholder: "0.00",
	},
	{
		key: "status",
		label: "Status",
		type: "select",
		required: true,
		options: [
			{ label: "Active", value: "active" },
			{ label: "Inactive", value: "inactive" },
		],
	},
];

const schema = z.object({
	name: z.string().min(1, "Name is required"),
	price: z.number().min(0, "Price must be positive"),
	status: z.enum(["active", "inactive"]),
});

// Mock data for demonstration
const mockProducts: Product[] = [
	{
		id: "1",
		name: "Wireless Headphones",
		price: 199.99,
		category: "electronics",
		status: "active",
		createdAt: "2024-01-15T10:00:00Z",
		updatedAt: "2024-01-15T10:00:00Z",
	},
	{
		id: "2",
		name: "Cotton T-Shirt",
		price: 29.99,
		category: "clothing",
		status: "active",
		createdAt: "2024-01-16T10:00:00Z",
		updatedAt: "2024-01-16T10:00:00Z",
	},
	{
		id: "3",
		name: "Coffee Mug",
		price: 15.99,
		category: "home",
		status: "inactive",
		createdAt: "2024-01-17T10:00:00Z",
		updatedAt: "2024-01-17T10:00:00Z",
	},
	{
		id: "4",
		name: "Smartphone",
		price: 699.99,
		category: "electronics",
		status: "active",
		createdAt: "2024-01-18T10:00:00Z",
		updatedAt: "2024-01-18T10:00:00Z",
	},
	{
		id: "5",
		name: "Running Shoes",
		price: 89.99,
		category: "sports",
		status: "active",
		createdAt: "2024-01-19T10:00:00Z",
		updatedAt: "2024-01-19T10:00:00Z",
	},
];

const handlers: CrudHandlers<Product> = {
	onFetch: async (params) => {
		// Simulate API delay
		await new Promise((resolve) => setTimeout(resolve, 500));

		let filteredData = [...mockProducts];

		// Apply search
		if (params.search) {
			const searchTerm = params.search.toLowerCase();
			filteredData = filteredData.filter(
				(product) =>
					product.name.toLowerCase().includes(searchTerm) || product.category.toLowerCase().includes(searchTerm),
			);
		}

		// Apply filters
		for (const [key, filter] of Object.entries(params.filters)) {
			filteredData = filteredData.filter((product) => {
				const value = product[key as keyof Product];
				switch ((filter as any).operator) {
					case "contains":
						return String(value)
							.toLowerCase()
							.includes(String((filter as any).value).toLowerCase());
					case "eq":
						return value === (filter as any).value;
					case "gt":
						return Number(value) > Number((filter as any).value);
					case "gte":
						return Number(value) >= Number((filter as any).value);
					case "lt":
						return Number(value) < Number((filter as any).value);
					case "lte":
						return Number(value) <= Number((filter as any).value);
					default:
						return true;
				}
			});
		}

		// Apply sorting
		if (params.sort) {
			const sortConfig = params.sort;
			filteredData.sort((a, b) => {
				const aValue = a[sortConfig.field as keyof Product];
				const bValue = b[sortConfig.field as keyof Product];
				const direction = sortConfig.direction === "asc" ? 1 : -1;

				if (aValue < bValue) return -1 * direction;
				if (aValue > bValue) return 1 * direction;
				return 0;
			});
		}

		// Apply pagination
		const start = (params.page - 1) * params.pageSize;
		const end = start + params.pageSize;
		const paginatedData = filteredData.slice(start, end);

		return {
			data: paginatedData,
			total: filteredData.length,
		};
	},

	onCreate: async (data) => {
		await new Promise((resolve) => setTimeout(resolve, 1000));
		const newProduct: Product = {
			...data,
			id: Date.now().toString(),
			createdAt: new Date().toISOString(),
			updatedAt: new Date().toISOString(),
		};
		mockProducts.push(newProduct);
		return newProduct;
	},

	onUpdate: async (id, data) => {
		await new Promise((resolve) => setTimeout(resolve, 1000));
		const index = mockProducts.findIndex((p) => p.id === id);
		if (index === -1) throw new Error("Product not found");

		mockProducts[index] = {
			...mockProducts[index],
			...data,
			updatedAt: new Date().toISOString(),
		};
		return mockProducts[index];
	},

	onDelete: async (id) => {
		await new Promise((resolve) => setTimeout(resolve, 1000));
		const index = mockProducts.findIndex((p) => p.id === id);
		if (index === -1) throw new Error("Product not found");
		mockProducts.splice(index, 1);
	},

	onBulkDelete: async (ids) => {
		await new Promise((resolve) => setTimeout(resolve, 1000));
		for (const id of ids) {
			const index = mockProducts.findIndex((p) => p.id === id);
			if (index !== -1) mockProducts.splice(index, 1);
		}
	},
};

const config: CrudConfig<Product> = {
	entityName: "Product",
	entityNamePlural: "Products",
	columns,
	formFields,
	formSchema: schema,
	handlers,

	features: {
		create: true,
		read: true,
		update: true,
		delete: true,
		bulkDelete: true,
		export: true,
		search: true,
		filter: true,
		sort: true,
		pagination: true,
	},

	ui: {
		tableSize: "small",
		formLayout: "vertical",
		formModalSize: "lg",
		showRowNumbers: true,
		showRefreshButton: true,
	},
};

export function ProductManagement() {
	return (
		<div className="container mx-auto py-6">
			<CrudTemplate config={config} queryKey={["products"]} />
		</div>
	);
}

export default ProductManagement;
